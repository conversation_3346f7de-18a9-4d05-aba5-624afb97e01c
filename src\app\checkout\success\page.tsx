'use client';

import React, { Suspense } from 'react';
import CheckoutSuccessContent from './CheckoutSuccessContent';

// Force this page to be dynamic (not prerendered)
export const dynamic = 'force-dynamic';



function LoadingFallback() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-100 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center">
      <div className="max-w-md mx-auto bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8 text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto mb-4"></div>
        <h1 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
          Loading...
        </h1>
        <p className="text-gray-600 dark:text-gray-300">
          Please wait while we load your subscription details.
        </p>
      </div>
    </div>
  );
}

export default function CheckoutSuccessPage() {
  return (
    <Suspense fallback={<LoadingFallback />}>
      <CheckoutSuccessContent />
    </Suspense>
  );
}
