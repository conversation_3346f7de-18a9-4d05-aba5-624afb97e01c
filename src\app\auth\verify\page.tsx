'use client';

import React, { Suspense } from 'react';
import Image from 'next/image';
import VerifyContent from './VerifyContent';

// Force this page to be dynamic (not prerendered)
export const dynamic = 'force-dynamic';

function VerifyLoadingFallback() {
  return (
    <div className="min-h-screen bg-slate-900 flex items-center justify-center p-4">
      <div className="max-w-md w-full">
        <div className="text-center mb-8">
          <Image
            src="/logo.svg"
            alt="Navtransfer"
            width={48}
            height={32}
            className="h-8 w-auto mx-auto invert mb-4"
          />
          <h1 className="text-2xl font-bold text-white">Navtransfer</h1>
        </div>

        <div className="bg-slate-800 rounded-lg p-6 text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <h2 className="text-lg font-medium text-white mb-2">Loading...</h2>
          <p className="text-slate-400">Please wait while we load the verification page</p>
        </div>
      </div>
    </div>
  );
}



export default function VerifyPage() {
  return (
    <Suspense fallback={<VerifyLoadingFallback />}>
      <VerifyContent />
    </Suspense>
  );
}
