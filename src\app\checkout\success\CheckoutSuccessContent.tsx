'use client';

import { useEffect, useState } from 'react';
import { useSearchParams } from 'next/navigation';
import { useSubscription } from '@/hooks/useSubscription';

export default function CheckoutSuccessContent() {
  const searchParams = useSearchParams();
  const { refreshSubscription } = useSubscription();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const sessionId = searchParams.get('session_id');
    
    if (!sessionId) {
      setError('No session ID found');
      setLoading(false);
      return;
    }

    // For demo purposes, extract email hash and tier from session ID
    if (sessionId.startsWith('demo_')) {
      const parts = sessionId.split('_');
      if (parts.length === 3) {
        const emailHash = parts[1];
        const tier = parts[2];
        
        // Simulate subscription activation
        setTimeout(async () => {
          try {
            await refreshSubscription();
            setLoading(false);
          } catch (error) {
            setError('Failed to refresh subscription');
            setLoading(false);
          }
        }, 1000);
      } else {
        setError('Invalid session ID format');
        setLoading(false);
      }
    } else {
      // In a real implementation, you would verify the session with Stripe
      setLoading(false);
    }
  }, [searchParams, refreshSubscription]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-100 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center">
        <div className="max-w-md mx-auto bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8 text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto mb-4"></div>
          <h1 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
            Processing your subscription...
          </h1>
          <p className="text-gray-600 dark:text-gray-300">
            Please wait while we activate your account.
          </p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-red-50 to-pink-100 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center">
        <div className="max-w-md mx-auto bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8 text-center">
          <div className="w-16 h-16 mx-auto mb-4 text-red-500">
            <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <h1 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
            Subscription Error
          </h1>
          <p className="text-gray-600 dark:text-gray-300 mb-4">
            {error}
          </p>
          <a
            href="/"
            className="inline-block bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
          >
            Return Home
          </a>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-100 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center">
      <div className="max-w-md mx-auto bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8 text-center">
        <div className="w-16 h-16 mx-auto mb-4 text-green-500">
          <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        </div>
        
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
          Subscription Activated!
        </h1>
        
        <p className="text-gray-600 dark:text-gray-300 mb-6">
          Thank you for subscribing to Navtransfer. Your account has been upgraded and you now have access to TURN relay servers for reliable file transfers.
        </p>

        <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4 mb-6">
          <h3 className="font-medium text-green-800 dark:text-green-200 mb-2">
            What's included:
          </h3>
          <ul className="text-sm text-green-700 dark:text-green-300 space-y-1">
            <li>• TURN relay servers for reliable transfers</li>
            <li>• Monthly quota for relay usage</li>
            <li>• Priority support</li>
            <li>• Faster polling intervals</li>
          </ul>
        </div>

        <div className="space-y-3">
          <a
            href="/"
            className="block w-full bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors font-medium"
          >
            Start Transferring Files
          </a>
          
          <p className="text-xs text-gray-500 dark:text-gray-400">
            You'll receive a confirmation email shortly with your subscription details.
          </p>
        </div>
      </div>
    </div>
  );
}
