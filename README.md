# 🚀 Navtransfer

**Secure, peer-to-peer file transfer with magic link authentication**

A modern file transfer application that prioritizes user experience, security, and reliability. Built with Next.js 15, WebRTC, and a passwordless authentication system.

## ✨ Features

### 🔐 **Magic Link Authentication**
- **No passwords required** - Sign in with just your email
- **Cross-device access** - Use your subscription on any device
- **Bulletproof recovery** - Never lose access to what you paid for
- **6-month persistent sessions** - Stay signed in across devices

### 📁 **File Transfer**
- **Direct P2P Transfer** - Files sent directly between devices using WebRTC
- **No file size limits** - Transfer files of any size
- **End-to-end encryption** - All transfers are encrypted
- **No server storage** - Files never touch our servers
- **Real-time progress** - See transfer progress in real-time
- **TURN relay support** - Works behind firewalls and NAT

### 💰 **Subscription Tiers**
- **Free** - Basic P2P transfers
- **Supporter ($1/month)** - 50GB TURN relay quota + faster connections
- **Pro ($2/month)** - 200GB TURN relay quota + priority routing

### 🎨 **User Experience**
- **Dark mode interface** - Easy on the eyes
- **100vh layout** - Perfect viewport utilization
- **WeTransfer-style UX** - Instant file upload access
- **Mobile responsive** - Works on all devices
- **Swiss clockwork precision** - Every pixel matters

## Quick Start

1. **Clone and install dependencies:**
```bash
git clone <repository-url>
cd navtransfer
pnpm install
```

2. **Set up environment variables:**
```bash
cp .env.example .env.local
# Edit .env.local with your configuration
```

3. **Run the development server:**
```bash
pnpm dev
```

4. **Open [http://localhost:3000](http://localhost:3000)**

## Architecture

### Frontend
- **Next.js 14** with App Router
- **TypeScript** for type safety
- **TailwindCSS** for styling
- **WebRTC** for peer-to-peer connections

### Signaling
- **Vercel Edge Functions** with HTTPS long-polling
- **In-memory storage** for room metadata (15-minute cleanup)
- **Rate limiting** and security headers

### Subscription System
- **PayPal Subscriptions** for payment processing (5% + $0.05 micropayments)
- **Upstash KV** for quota tracking (demo uses in-memory)
- **JWT tokens** for authentication
- **SHA-256 hashed emails** for privacy

### Security
- **TLS 1.3** everywhere
- **Content Security Policy** headers
- **HSTS** and security headers
- **DOMPurify** for input sanitization
- **Rate limiting** on all endpoints

## Environment Variables

Create a `.env.local` file with the following variables:

```bash
# PayPal Configuration
PAYPAL_CLIENT_ID=your-paypal-client-id
PAYPAL_CLIENT_SECRET=your-paypal-client-secret
PAYPAL_WEBHOOK_ID=your-paypal-webhook-id
PAYPAL_ENVIRONMENT=sandbox # or live

# TODO: Stripe Configuration (future work)
# STRIPE_SECRET_KEY=sk_test_...
# STRIPE_PUBLISHABLE_KEY=pk_test_...
# STRIPE_WEBHOOK_SECRET=whsec_...

# Upstash Redis Configuration
UPSTASH_REDIS_REST_URL=https://...
UPSTASH_REDIS_REST_TOKEN=...

# JWT Secret for subscription tokens
JWT_SECRET=your-super-secret-jwt-key

# TURN Server Configuration (for paid tiers)
TURN_SERVER_URL=turn:your-turn-server.com:3478
TURN_USERNAME=username
TURN_PASSWORD=password

# App Configuration
NEXT_PUBLIC_APP_URL=http://localhost:3000
```

## ✅ Current Status

- ✅ **Core P2P file transfer** - Working with WebRTC
- ✅ **PayPal integration** - Demo mode (see PAYPAL_INTEGRATION.md)
- ✅ **Security headers** - CSP, CORS, rate limiting
- ✅ **Subscription system** - JWT-based with KV storage
- ✅ **TURN relay support** - For paid tiers
- ✅ **End-to-end encryption** - AES-GCM
- ✅ **Responsive UI** - Mobile-friendly design
- ✅ **Build system** - Next.js 15 with TypeScript
- 🔄 **Production deployment** - Ready for Vercel/Netlify
- 📋 **TODO**: Enable real PayPal SDK and Upstash KV

## PayPal Setup

1. **Create a PayPal Developer account** at [developer.paypal.com](https://developer.paypal.com)

2. **Create a new app** and get your Client ID and Secret

3. **Create subscription plans:**
   - Supporter: $1/month (save the plan ID)
   - Pro: $2/month (save the plan ID)

4. **Set up webhook endpoint:**
   - URL: `https://your-domain.com/api/webhook/paypal`
   - Events: `BILLING.SUBSCRIPTION.ACTIVATED`, `BILLING.SUBSCRIPTION.CANCELLED`, `PAYMENT.SALE.COMPLETED`

5. **Configure environment variables** with your PayPal credentials

6. **PayPal Pricing Structure:**
   - Micropayments: 5% + $0.05 per transaction
   - Supporter ($1): Fee $0.10, Net $0.90
   - Pro ($2): Fee $0.15, Net $1.85

## Stripe Setup (Future Work)

TODO: Stripe integration is marked for future implementation. The current system uses PayPal subscriptions.

## TURN Server Deployment

For paid tiers, you'll need a TURN server for relay when P2P fails:

1. **Deploy coturn on a VPS:**
```bash
# Ubuntu/Debian
sudo apt-get install coturn

# Configure /etc/turnserver.conf
listening-port=3478
tls-listening-port=5349
realm=your-domain.com
server-name=your-domain.com
lt-cred-mech
user=username:password
```

2. **Start the service:**
```bash
sudo systemctl enable coturn
sudo systemctl start coturn
```

3. **Update environment variables** with TURN credentials

## Quota Mathematics

### Cost Projections

**TURN Relay Usage:**
- Supporter (50GB/month): ~$2.50 server cost
- Pro (200GB/month): ~$10 server cost
- Pricing covers 2x server costs + development

**Vercel Function Limits:**
- Free tier: 1M invocations/month
- Typical handshake: 6-10 invocations
- Capacity: ~100k transfers/month on free tier

### Quota Tracking

Quotas reset on the 1st of each month via cron job:
```bash
# Add to Vercel cron jobs
0 0 1 * * curl -X PUT https://your-domain.com/api/quota/reset \
  -H "Authorization: Bearer $CRON_SECRET"
```

## Testing

### Unit Tests
```bash
pnpm test          # Run tests in watch mode
pnpm test:run      # Run tests once
pnpm test:ui       # Run tests with UI
```

### End-to-End Tests
```bash
pnpm test:e2e      # Run Playwright tests
pnpm test:e2e:ui   # Run with Playwright UI
```

### Security Audit
```bash
pnpm audit         # Check for vulnerabilities
```

## Deployment

### Vercel (Recommended)

1. **Connect your repository** to Vercel
2. **Set environment variables** in Vercel dashboard
3. **Deploy** - automatic on git push

### Manual Deployment

```bash
pnpm build
pnpm start
```

## Security Checklist

- [x] **TLS 1.3** enforced everywhere
- [x] **Content Security Policy** headers
- [x] **HSTS** with preload
- [x] **X-Frame-Options** DENY
- [x] **Referrer-Policy** strict-origin-when-cross-origin
- [x] **Rate limiting** on all endpoints
- [x] **Input sanitization** with DOMPurify
- [x] **File type validation** and size limits
- [x] **No server-side file storage**
- [x] **Hashed email addresses** only
- [x] **Automatic data cleanup** (15 min max)
- [x] **End-to-end encryption** (AES-GCM)
- [x] **Dependency vulnerability scanning**

## Privacy Guarantees

1. **Files never stored server-side** - Direct P2P transfer only
2. **Signaling metadata only** - Deleted within 15 minutes
3. **No IP logging** - Edge logs auto-delete within 24h
4. **Hashed emails only** - SHA-256, no plaintext storage
5. **No tracking cookies** - Session-only, functional cookies
6. **End-to-end encryption** - Server cannot decrypt files
7. **Open source** - Auditable privacy implementation

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

## License

MIT License - see LICENSE file for details

## Support

For support, please open an issue on GitHub or contact us through the app.
