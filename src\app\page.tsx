'use client';

import Image from 'next/image';
import { MainTransfer } from '@/components/MainTransfer';
import { ConversionSidebar } from '@/components/ConversionSidebar';
import { useSubscription } from '@/hooks/useSubscription';

export default function Home() {
  return (
    <div className="h-screen bg-slate-900 flex flex-col overflow-hidden">
      {/* Header */}
      <header className="bg-slate-800 border-b border-slate-700 flex-shrink-0">
        <div className="px-6 py-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <Image
                src="/logo.svg"
                alt="Navtransfer"
                width={32}
                height={21}
                className="h-6 w-auto invert"
                priority
              />
            </div>
            <StatusIndicator />
          </div>
        </div>
      </header>

      <main className="flex flex-1 overflow-hidden">
        {/* Main Transfer Area - 70% */}
        <div className="flex-1 overflow-auto">
          <MainTransfer />
        </div>

        {/* Conversion Sidebar - 30% */}
        <div className="w-80 bg-slate-800 border-l border-slate-700 overflow-auto">
          <ConversionSidebar />
        </div>
      </main>
    </div>
  );
}

function StatusIndicator() {
  const { subscription, isLoading, isAuthenticated } = useSubscription();

  if (isLoading) {
    return (
      <div className="flex items-center space-x-2 px-3 py-1 bg-slate-700 text-slate-300 rounded-full text-sm">
        <div className="w-2 h-2 rounded-full bg-slate-500 animate-pulse" />
        <span>Loading...</span>
      </div>
    );
  }

  const isPaid = subscription?.tier && subscription.tier !== 'free';
  const statusColor = isPaid ? 'bg-emerald-500' : 'bg-slate-500';
  const displayTier = subscription?.tier || 'Free';

  return (
    <div className="flex items-center space-x-2 px-3 py-1 bg-slate-700 text-slate-300 rounded-full text-sm">
      <div className={`w-2 h-2 rounded-full ${statusColor}`} />
      <span className="capitalize">
        {displayTier}
        {isAuthenticated && isPaid && (
          <span className="ml-1 text-xs text-emerald-400">✓</span>
        )}
      </span>
    </div>
  );
}
