'use client';

import { useEffect, useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import Image from 'next/image';

export default function VerifyContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [message, setMessage] = useState('');

  useEffect(() => {
    const token = searchParams.get('token');
    
    if (!token) {
      setStatus('error');
      setMessage('Invalid verification link');
      return;
    }

    // The verification is handled by the API route
    // This page is just for showing the result
    const urlParams = new URLSearchParams(window.location.search);
    const error = urlParams.get('error');
    const auth = urlParams.get('auth');

    if (error) {
      setStatus('error');
      switch (error) {
        case 'invalid_token':
          setMessage('Invalid verification link');
          break;
        case 'expired_token':
          setMessage('Verification link has expired');
          break;
        case 'verification_failed':
          setMessage('Verification failed. Please try again.');
          break;
        default:
          setMessage('Something went wrong');
      }
    } else if (auth === 'success') {
      setStatus('success');
      setMessage('Successfully signed in!');
      
      // Redirect to home page after 2 seconds
      setTimeout(() => {
        router.push('/');
      }, 2000);
    } else {
      // If no error or success param, try to verify the token
      fetch(`/api/auth/verify?token=${token}`)
        .then(response => {
          if (response.redirected) {
            // Follow the redirect
            window.location.href = response.url;
          } else {
            setStatus('error');
            setMessage('Verification failed');
          }
        })
        .catch(() => {
          setStatus('error');
          setMessage('Verification failed');
        });
    }
  }, [searchParams, router]);

  return (
    <div className="min-h-screen bg-slate-900 flex items-center justify-center p-4">
      <div className="max-w-md w-full">
        <div className="text-center mb-8">
          <Image
            src="/logo.svg"
            alt="Navtransfer"
            width={48}
            height={32}
            className="h-8 w-auto mx-auto invert mb-4"
          />
          <h1 className="text-2xl font-bold text-white">Navtransfer</h1>
        </div>

        <div className="bg-slate-800 rounded-lg p-6 text-center">
          {status === 'loading' && (
            <>
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-4"></div>
              <h2 className="text-lg font-medium text-white mb-2">Verifying...</h2>
              <p className="text-slate-400">Please wait while we sign you in</p>
            </>
          )}

          {status === 'success' && (
            <>
              <div className="w-8 h-8 bg-emerald-500 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
              </div>
              <h2 className="text-lg font-medium text-white mb-2">Success!</h2>
              <p className="text-slate-400 mb-4">{message}</p>
              <p className="text-sm text-slate-500">Redirecting you to the app...</p>
            </>
          )}

          {status === 'error' && (
            <>
              <div className="w-8 h-8 bg-red-500 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              </div>
              <h2 className="text-lg font-medium text-white mb-2">Verification Failed</h2>
              <p className="text-slate-400 mb-4">{message}</p>
              <button
                onClick={() => router.push('/')}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors"
              >
                Back to Home
              </button>
            </>
          )}
        </div>
      </div>
    </div>
  );
}
